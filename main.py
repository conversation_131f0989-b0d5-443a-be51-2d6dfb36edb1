"""
Schedule Generator Web App

A Flask web application that generates customizable school schedules in SVG format.
Allows users to input class names and titles through a web interface.
Handles schedule generation, validation, and error handling.
"""

import json
from flask import Flask, Response, render_template, request, redirect, url_for
from daschedule import normalize_classes, create_svg, CLASSES, TITLE_TEXT, ROOMS, TEACHERS, DEFAULT_FREE_PERIOD_NAME

app = Flask(__name__)

@app.route("/")
def index():
    """
    Render homepage with schedule_form.html template
    """
    default_classes = {str(k): v for k, v in CLASSES.items()}
    default_rooms = {str(k): v for k, v in ROOMS.items()}
    default_teachers = {str(k): v for k, v in TEACHERS.items()}
    return render_template("schedule_form.html",
                           classes=default_classes,
                           rooms=default_rooms,
                           teachers=default_teachers,
                           title=TITLE_TEXT,
                           free_block=DEFAULT_FREE_PERIOD_NAME,
                           disable_freeblocks=False)

@app.route("/generate", methods=["POST"])
def generate_schedule():
    """
    Generate SVG schedule based on POST form data.

    Expects:
        - title: Schedule title (default: "Schedule")
        - classes: JSON string mapping period numbers to class names

    Returns:
        - SVG Response on success
        - Error message with status code on failure
    """
    try:
        title = request.form.get("title", "Schedule")
        free_period_name = request.form.get("free_block", "").strip() or "Study Period"
        disable_freeblocks = request.form.get("disable_freeblocks") == "on"
        classes_json = request.form.get("classes", "{}")

        raw_classes = json.loads(classes_json)

        # Extract just the 'name' for normalize_classes
        classes = {
            str(k): v.get("name", "").strip()
            for k, v in raw_classes.items()
            if isinstance(v, dict)
        }

        # Extract room data
        rooms = {
            str(k): v.get("room", "").strip()
            for k, v in raw_classes.items()
            if isinstance(v, dict)
        }

        # Extract teacher data
        teachers = {
            str(k): v.get("teacher", "").strip()
            for k, v in raw_classes.items()
            if isinstance(v, dict)
        }

        # Fill empty periods with free block name (unless disabled)
        classes = normalize_classes(classes, free_period_name, fill_empty_periods=not disable_freeblocks)

        if classes is None:
            return "Error: Invalid classes format", 400
        if classes == 1:
            return "Error: Too many classes missing. (max: 2)", 400

        svg_content = create_svg(classes, rooms, teachers, title, free_period_name, exact_dimension=False)

        return Response(svg_content, mimetype='image/svg+xml')

    except json.JSONDecodeError:
        return "Error: Invalid JSON format for classes", 400
    except Exception as e:
        return f"Error generating schedule: {str(e)}", 500

@app.route("/generate", methods=["GET"])
def generate_schedule_get():
    """
    Redirect user back to homepage if they try to open /generate in the browser
    """
    return redirect(url_for("index"))

if __name__ == "__main__":
    app.run()
